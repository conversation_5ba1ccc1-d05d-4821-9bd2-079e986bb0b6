<template>
  <div class="working">
    <img src="@/assets/images/login/power.png" alt="" width="362" height="362" />
    <div class="text">抱歉，您暂无权限访问此页面</div>
    <div class="word">如需权限，请联系管理员开通</div>
    <div class="word margin_t_8">手机号：13901580390</div>
    <div class="word">邮箱：<EMAIL></div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs } from "vue";

export default defineComponent({
  setup() {
    const data = reactive({});
    return {
      ...toRefs(data),
    };
  },
});
</script>

<style lang="scss" scoped>
.working {
  position: absolute;
  left: 50%;
  top: 45%;
  transform: translate(-50%, -50%);
  text-align: center;
  line-height: normal;
}
.text {
  font-weight: 500;
  font-size: 18px;
  color: #00060E;
  margin-top: -60px;
}
.word {
  font-weight: 400;
  font-size: 16px;
  color: #999999;
  line-height: normal;
}
</style>