<template>
  <el-config-provider :locale="zhCn">
    <div class="modelContent">
      <div class="title">
        <img src="@/assets/images/community/review.png" width="60px" height="27px" style="margin-right: 10px" alt="" />
      </div>
      <div class="margin_t_20">
        <a-descriptions bordered class="margin_b_20">
          <a-descriptions-item label="办理人">{{ formState.createName }}</a-descriptions-item>
          <a-descriptions-item label="办理部门">{{ formState.orgPathName }}</a-descriptions-item>
          <a-descriptions-item label="办理时间">{{ formState.createTime }}</a-descriptions-item>
        </a-descriptions>
        <div class="card_table">
          <div class="table_left">
            <div class="margin_t_4" style="letter-spacing: 4px;text-align: left;">
              <span class="warning" style="color: red">*</span>
              <span>反馈内容</span>
            </div>
          </div>
          <div class="table_right inputTable">
            <a-textarea v-model:value="formState.inputValue" placeholder="请输入内容" :rows="4" showCount :maxlength="500" />
          </div>
        </div>
        <div class="card_table">
          <div class="table_left">
            <div class="margin_t_4" style="text-align: left;">预计完成时间</div>
          </div>
          <div class="table_right inputTable">
            <el-date-picker class="custom-date-picker" :disabled-date="disabledDate" v-model="formState.finishTime"
              placeholder="请选择预计完成时间" :value-format="'YYYY-MM-DD'" />
          </div>
        </div>
        <div class="card_table">
          <div class="table_left table_line">
            <div class="margin_t_4" style="text-align: left;">
              <span style="margin-right: 55px">附</span>件
            </div>
          </div>
          <div class="table_right table_line" style="display: flex; align-items: center">
            <a-upload
              v-model:file-list="formState.file"
              :customRequest="uploadFile"
              :multiple="false"
              :max-count="1"
              accept=".doc,.docx,.xls,.xlsx,.pdf,.jpg,.jpeg,.png,.mp4,.mov,.avi"
              :show-upload-list="false"
            >
              <a-button
                v-if="ShowBtn"
                style="
                  background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
                  border-radius: 4px 4px 4px 4px;
                  font-weight: 500;
                  font-size: 14px;
                  color: #ffffff;
                  line-height: 20px;
                "
                :loading="uploading"
              >
                上传附件
              </a-button>
              <template v-if="!ShowBtn">
                <div class="custom-upload-item">
                  <span
                    class="file-name"
                    v-if="formState.file && formState.file.length > 0"
                    >{{ formState.file[0].fileName }}</span
                  >
                  <a-tooltip title="删除">
                    <a-button
                      type="text"
                      danger
                      @click.stop="handleRemove"
                      class="delete-btn"
                    >
                      <delete-outlined v-if="formState.file" />
                    </a-button>
                  </a-tooltip>
                </div>
              </template>
            </a-upload>
            <div
              v-if="formState.file.length < 1"
              style="
                font-size: 14px;
                color: rgba(0, 0, 0, 0.25);
                margin-left: 14px;
              "
            >
              注：视频限制20M以内
            </div>
          </div>
        </div>
      </div>

      <div class="flex just-center align-center margin_t_32">
        <a-button :loading="auditLoading" type="primary" @click="submitAdd" style="
            background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
            border-radius: 4px;
            font-weight: 500;
            border: none;
            margin-left: 16px;
          ">
          确认
        </a-button>
        <a-button @click="handleReassignment" type="primary" style="
            background: linear-gradient(90deg, #ff845f 0%, #ff3730 100%);
            border-radius: 4px;
            font-weight: 500;
            border: none;
            margin-left: 16px;
          ">
          重派
        </a-button>
      </div>
    </div>
  </el-config-provider>
</template>
<script>
import { defineComponent, reactive, toRefs, computed } from "vue";
import moment from "moment";
import { ElConfigProvider } from "element-plus";
import zhCn from "element-plus/es/locale/lang/zh-cn";
import { uploadFileList } from "@/api/fileUpload/uploadFile.js";
import { message } from "ant-design-vue";
import {
  DeleteOutlined,
  FileTextOutlined,
  PictureOutlined,
  VideoCameraOutlined,
} from "@ant-design/icons-vue";
import { useRouter } from "vue-router";
import { setFeedback } from "@/api/community/index.js";
import eventBus from "@/utils/eventBus";
export default defineComponent({
  props: {
    typeObject: {
      type: Object,
      default: () => { },
    },
    id: {
      type: [String, Number],
      default: undefined,
    },
  },
  components: {
    ElConfigProvider,
    DeleteOutlined,
    FileTextOutlined,
    PictureOutlined,
    VideoCameraOutlined,
  },
  emits: ["cancel-idea", "refresh-view", "reassignment"],
  setup(props, { emit }) {
    const data = reactive({
      auditLoading: false,
      uploading: false,
      id: props.id,
      typeObject: computed(() => props.typeObject || {}),
      formState: {
        createName: props.typeObject.item.createName || "",
        orgPathName: props.typeObject.item.orgPathName || "",
        createTime: props.typeObject.item.createTime || "",
        inputValue: props.typeObject.item.content || "",
        finishTime: props.typeObject.item.finishTime || "",
        file: props.typeObject.item.fileName ? [{
          fileName: props.typeObject.item.fileName,
          filePath: props.typeObject.item.filePath,
          fileUrl: props.typeObject.item.fileUrl,
        }] : [],
      },
    });
    const ShowBtn = computed(() => {
      return !data.formState.file || data.formState.file.length === 0;
    });
    const Router = useRouter();
    const disabledDate = (current) => {
      return current && current < moment().subtract(1, "days").endOf("day");
    };

    const handleReassignment = () => {
      emit("reassignment");
    };

    const uploadFile = async (info) => {
      const file = info.file;
      const isVideo = file.type.startsWith("video/");
      const maxVideoSize = 20 * 1024 * 1024;
      if (isVideo && file.size > maxVideoSize) {
        message.error("视频文件大小不能超过20MB");
        info.onError();
        return;
      }
      data.uploading = true;
      try {
        let formData = new FormData();
        formData.append("file", info.file);
        const result = await uploadFileList(formData);
        if (result.code === 200) {
          if (!data.formState.file) {
            data.formState.file = [{}];
          }
          data.formState.file[0] = {
            fileName: result.data.fileName,
            filePath: result.data.filePath,
            fileUrl: result.data.fileUrl,
          };
          message.success("上传成功");
          info.onSuccess();
          return result;
        } else {
          message.error("上传失败");
          info.onError();
        }
      } catch (error) {
        message.error("上传出错");
        info.onError();
      } finally {
        data.uploading = false;
      }
    };

    const handleRemove = (e) => {
      e.stopPropagation();
      data.formState.file = [];
    };
    const submitAdd = async () => {
      try {
        const { inputValue, file, finishTime } = data.formState;
        const { type } = data.typeObject;

        if (!inputValue?.trim()) {
          message.error("请填写内容！");
          return;
        }

        // 添加文件信息
        const fileInfo = {
          fileName: file[0]?.fileName,
          filePath: file[0]?.filePath,
          fileUrl: file[0]?.fileUrl,
        };

        // 合并数据
        let requestData;
        requestData = {
          id: data.id,
          auditResult: type,
          feedback: {
            content: inputValue.trim(),
            ...fileInfo,
            finishTime,
          },
        };

        data.auditLoading = true;
        const successMessage = "反馈成功";
        const errorMessage = "反馈失败";
        console.log(requestData);

        const res = await setFeedback(requestData);

        if (res.code !== 200) {
          throw new Error(res.message || errorMessage);
        }

        message.success(successMessage);
        emit("refresh-view");
        eventBus.emit("communityTable");
        Router.push({
          name: "communityHome",
        });
        await new Promise((resolve) => setTimeout(resolve, 100));
        emit("cancel-idea");
      } catch (error) {
        message.error(error.message || "提交失败");
      } finally {
        data.auditLoading = false;
      }
    };
    return {
      ...toRefs(data),
      disabledDate,
      handleRemove,
      Router,
      handleReassignment,
      uploadFile,
      submitAdd,
      zhCn,
      ShowBtn,
    };
  },
});
</script>

<style lang="scss" scoped>
// ::v-deep(.custom-date-picker.el-input__inner) {
//   border: none;
//   background-color: transparent;
// }
:deep(.ant-descriptions-item-label) {
  width: 154px !important;
  min-width: 154px !important;
}

::v-deep(.custom-date-picker, .el-input__wrapper) {
  width: 426px;
}

// ::v-deep(textarea.ant-input) {
//   border: none;
// }
.modelContent {
  margin: 0 16px;
}

.title {
  margin-top: -8px;
}

.card_table {
  display: flex;

  .table_left {
    width: 154px;
    min-height: 46px;
    padding-left: 24px;
    // background: #f5f7fc;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    text-align: right;
    // border: 1px solid #e8eaed;
    border-right: none;
    border-bottom: none;
  }

  .table_right {
    flex: 1;
    min-height: 46px;
    color: rgba(0, 0, 0, 0.85);
    // border: 1px solid #e8eaed;
    // padding: 0 16px;
    border-left: none;
    border-bottom: none;
  }

  .inputTable {
    padding: 0;
  }

  // .table_line {
  //   border-bottom: 1px solid #e8eaed;
  // }

  .tableFile {
    color: #0c70eb;
    border-bottom: 1px solid #0c70eb;
    width: fit-content;
    cursor: pointer;
  }

  .custom-upload-item {
    display: flex;
    align-items: center;
    min-height: 48px;
    padding-left: 16px;
    margin-left: -16px;
    border-radius: 4px;
    background: #fafafa;
  }

  .custom-upload-item .anticon {
    margin-right: 8px;
    font-size: 16px;
    color: #1890ff;
  }

  .file-name {
    flex: 1;
    padding-right: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 14px;
  }

  .delete-btn {
    flex-shrink: 0;
    color: #ff4d4f;
  }

  .delete-btn:hover {
    background: rgba(255, 77, 79, 0.1);
  }
}
</style>