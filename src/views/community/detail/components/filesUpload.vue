<template>
  <div class="file-upload-container">
    <div v-if="modeType === 'upload' && !isMaxFilesUploaded" class="upload-header">
      <a-upload :customRequest="uploadFile" :max-count="maxCount" :accept="acceptTypes" :show-upload-list="false">
        <a-button class="upload-button" :loading="uploading">上传附件</a-button>
      </a-upload>
      <div class="upload-note">
        注：视频限制{{ maxVideoSize / (1024 * 1024) }}M 以内
      </div>
    </div>

    <!-- 文件列表 -->
    <div v-if="Object.keys(groupedFiles).length > 0" class="grouped-file-list">
      <div class="file-category" v-for="(files, fileType) in groupedFiles" :key="fileType">
        <span class="file-type-title">{{ getFileTypeDisplayName(fileType) }}</span>
        <div class="file-grid">
          <div v-for="(file, index) in files" :key="file.fileName + index" @click="previewFile(file)"
            class="file-item">
            <div class="file-icon">
              <img :src="getFileIcon(file.fileName)" class="file-type-icon">
            </div>

            <div class="file-info">
              <div class="file-name">{{ file.fileName }}</div>
              <div class="file-size">{{ formatFileSize(file.fileSize) }}<span v-if="modeType == 'upload'"
                  class="upload-status">上传完成</span></div>
            </div>

            <a-tooltip v-if="modeType == 'upload'" title="删除文件">
              <a-button type="text" danger @click.stop="handleRemove(file)" class="delete-btn" size="small">
                <delete-outlined style="font-size: 20px; color: #247fec;" />
              </a-button>
            </a-tooltip>
          </div>
        </div>
      </div>
    </div>
    <div v-else-if="modeType === 'detail'" class="no-files-container">
        暂无
    </div>
  </div>
</template>

<script>
import { message } from 'ant-design-vue';
import { DeleteOutlined } from '@ant-design/icons-vue';
import { uploadFileList } from "@/api/fileUpload/uploadFile.js";
import { reactive, ref, computed } from 'vue';
import aviIcon from '@/assets/images/community/AVI.png';
import movIcon from '@/assets/images/community/MOV.png';
import mp4Icon from '@/assets/images/community/MP4.png';
import jpegIcon from '@/assets/images/community/JPEG.png';
import jpgIcon from '@/assets/images/community/JPG.png';
import pngIcon from '@/assets/images/community/PNG.png';
import pdfIcon from '@/assets/images/community/PDF.png';
import wordIcon from '@/assets/images/community/word2x.png';
import xlsIcon from '@/assets/images/community/XLS.png';
export default {
  name: 'FileUpload',
  components: {
    DeleteOutlined
  },
  props: {
    modeType: {
      type: String,
      default: 'upload', // 上传模式，值为 'upload' 或 'detail'
    },
    // 文件列表
    modelValue: {
      type: Array,
      default: () => []
    },
    // 最大文件数量
    maxCount: {
      type: Number,
      default: 10
    },
    // 接受的文件类型
    acceptTypes: {
      type: String,
      default: ".doc,.docx,.xls,.xlsx,.pdf,.jpg,.jpeg,.png,.mp4,.mov,.avi"
    },
    // 视频文件最大大小（字节）
    maxVideoSize: {
      type: Number,
      default: 20 * 1024 * 1024 // 20MB
    }
  },
  emits: ['update:modelValue', 'preview-file'],
  setup(props, { emit }) {
    const uploading = ref(false);
    const validFiles = props.modelValue.filter(file => 
      file.fileName && file.fileName.trim() !== '' &&
      file.fileUrl && file.fileUrl.trim() !== ''
    );
    const internalFileList = reactive([...validFiles]);
    // 文件类型图标映射
    const fileIconMap = {
      'avi': aviIcon,
      'mov': movIcon,
      'mp4': mp4Icon,
      'jpeg': jpegIcon,
      'jpg': jpgIcon,
      'png': pngIcon,
      'pdf': pdfIcon,
      'doc': wordIcon,
      'docx': wordIcon,
      'xls': xlsIcon,
      'xlsx': xlsIcon,
    };
    // 计算属性
    const isMaxFilesUploaded = computed(() => internalFileList.length >= props.maxCount);

    const groupedFiles = computed(() => {
      return internalFileList.reduce((groups, file) => {
        const fileType = getFileType(file.fileName);
        if (!groups[fileType]) groups[fileType] = [];
        groups[fileType].push(file);
        return groups;
      }, {});
    });

    // 方法
    const getFileType = (fileName) => {
      if (!fileName) return "unknown";
      const ext = fileName.toLowerCase().split(".").pop();
      if (["doc", "docx"].includes(ext)) return "word";
      if (["xls", "xlsx"].includes(ext)) return "excel";
      if (["jpg", "jpeg", "png"].includes(ext)) return "image";
      if (["pdf"].includes(ext)) return "pdf";
      if (["mp4", "mov", "avi"].includes(ext)) return "video";
      return "unknown";
    };
    // 获取文件图标
    const getFileIcon = (fileName) => {
      if (!fileName) return fileIconMap.default;
      const ext = fileName.toLowerCase().split(".").pop();
      return fileIconMap[ext] || fileIconMap.default;
    };

    const getFileTypeDisplayName = (type) => {
      const mapping = {
        word: "Word 文件(.doc, .docx)",
        excel: "Excel 文件(.xls, .xlsx)",
        image: "图片文件(.jpg, .jpeg, .png)",
        pdf: "PDF 文件(.pdf)",
        video: "视频文件(.mp4, .mov, .avi)",
        unknown: "",
      };
      return mapping[type] || "";
    };

    const formatFileSize = (size) => {
      return size ? `${(size / 1024).toFixed(1)} KB` : "未知大小";
    };

    // 上传文件
    const uploadFile = async (info) => {
      const file = info.file;
      const isVideo = file.type.startsWith("video/");

      if (isVideo && file.size > props.maxVideoSize) {
        message.error(`视频文件大小不能超过 ${props.maxVideoSize / (1024 * 1024)}MB`);
        info.onError();
        return;
      }

      if (internalFileList.length >= props.maxCount) {
        message.error(`最多只能上传 ${props.maxCount} 个文件`);
        info.onError();
        return;
      }

      uploading.value = true;

      try {
        const formData = new FormData();
        formData.append("file", file);

        const result = await uploadFileList(formData);

        if (result.code === 200) {
          internalFileList.push({
            fileName: result.data.fileName,
            filePath: result.data.filePath,
            fileUrl: result.data.fileUrl,
            fileSize: file.size,
          });

          // 通知父组件更新 modelValue
          emit("update:modelValue", internalFileList);
          message.success("上传成功");
          info.onSuccess();
        } else {
          message.error("上传失败");
          info.onError();
        }
      } catch (error) {
        console.error("上传出错:", error);
        message.error("上传出错");
        info.onError();
      } finally {
        uploading.value = false;
      }
    };
    const handleRemove = (fileToRemove) => {
      const index = internalFileList.indexOf(fileToRemove);
      if (index >= 0) {
        internalFileList.splice(index, 1);
        emit("update:modelValue", internalFileList);
        message.success("文件已删除");
      }
    };

    const previewFile = async (e) => {
      try {
        const { fileUrl: href, fileName: downName } = e;
        const token = localStorage.getItem("token") || "";
        const fileExtension = downName.split(".").pop().toLowerCase();
        const isImage = ["jpg", "jpeg", "png"].includes(fileExtension);
        const isVideo = ["mp4", "mov", "avi", "wmv", "mkv", "flv"].includes(
          fileExtension
        );
        if (isVideo) {
          let type = 1;
          emit("preview-file", e, type);
        } else if (isImage) {
          let type = 2;
          emit("preview-file", e, type);
        } else {
          let downloadUrl = new URL(href, window.location.origin).href;
          if (href.includes(window.location.origin)) {
            downloadUrl = href.replace(window.location.origin, "/portal");
          }
          const urlObj = new URL(downloadUrl);
          urlObj.searchParams.set("token", token);
          downloadUrl = urlObj.toString();
          window.open(downloadUrl, "_blank");
        }
      } catch (error) {
        console.error("文件处理失败:", error);
      }
      return false;
    };

    return {
      uploading,
      internalFileList,
      isMaxFilesUploaded,
      groupedFiles,
      getFileType,
      getFileTypeDisplayName,
      formatFileSize,
      uploadFile,
      handleRemove,
      getFileIcon,
      previewFile
    };
  },
};
</script>

<style lang="scss" scoped>
.file-upload-container {
  margin-top: 10px;
  width: 100%;
}

.upload-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.upload-button {
  background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
  border-radius: 4px;
  font-weight: 500;
  font-size: 14px;
  color: #ffffff;
  line-height: 20px;
}

.upload-note {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.25);
  margin-left: 14px;
}

.grouped-file-list {
  margin-top: 16px;
}

.file-category {
  margin-bottom: 20px;
}

.file-type-title {
  font-weight: 400;
  font-size: 18px;
  color: #00060E;
  text-align: left;
  font-style: normal;
  margin-bottom: 8px;
}

.file-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  width: 100%;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: linear-gradient(90deg, #F7FAFF 0%, #F5F6FF 99%);
  box-shadow: 0px 0px 12px 0px rgba(197, 219, 254, 0.2);
  border-radius: 8px 8px 8px 8px;
  border: 2px solid #FFFFFF;
}

.file-icon {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  margin-right: 12px;
  overflow: hidden;
}

.file-type-icon {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.file-info {
  flex: 1;
}

.file-name {
  font-size: 16px;
  color: #00060E;
  font-weight: 500;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-size {
  font-size: 14px;
  color: #A2ABB5;
}

.upload-status {
  margin-left: 8px;
  font-weight: 400;
  font-size: 14px;
  color: #00BD62;
  line-height: 22px;
}
</style>