<template>
  <div>
    <div class="audit flex align-center"
      v-if="(userInfo.roleKeyList.includes('auditManager') && isRefuse == 1) || isRefuse > 1 || isRefuse == '-1'">
      <div v-if="(userInfo.roleKeyList.includes('auditManager') && isRefuse == 1) || isRefuse > 1 || isRefuse == '-1'">
        审核管理员审核意见
      </div>
      <a-button type="primary" @click="allotView" v-if="userInfo.roleKeyList.includes('auditManager') && isRefuse == 1"
        class="audit-button-margin-left">
        分配
      </a-button>
    </div>
    <div class="margin_t_16" v-if="(text && isRefuse != 1) || isRefuse == '-1'">
      <div class="flowPath">
        <div style="padding: 12px 16px">
          <div class="flex just-sb">
            <a-tag class="tag publish font_14" v-if="isRefuse > 1">已分配</a-tag>
            <a-tag class="tag refuse font_14" v-if="isRefuse == '-1'">已驳回</a-tag>
            <div class="time" v-if="dealTime">{{ dealTime }}</div>
          </div>
          <div class="desc white-space-pre-wrap">
            {{ auditReason }}
          </div>
          <div class="desc" v-if="text">请{{ text }}阅处。</div>
        </div>
      </div>
    </div>
    <div class="audit" v-if="showMainDepartmentSection">
      <div>主办部门反馈结果</div>
      <div class="flowPath margin_t_16" v-if="showMainDepartmentActions">
        <div style="padding: 12px 16px;gap: 16px;display:flex;">
          <a-button v-if="mainUserList.find(item => item.userId == userInfo.id)?.appointed != true"
            @click="transferTask" type="primary" class="feedback-button">
            转派
          </a-button>
          <a-button @click="feedBack" type="primary" class="feedback-button">
            情况反馈
          </a-button>
        </div>
      </div>
    </div>
    <div class="margin_t_16" v-if="showMainFeedback">
      <div class="flowPath" v-for="(item, key) in getMainFeedbackList" :key="key">
        <a-button v-if="key == 0 && item.appointed && !hasUserSubmittedFeedback" @click="handleReview(item)"
          type="primary" class="feedback-button-margin">
          审核
        </a-button>
        <div style="padding: 12px 16px">
          <div class="flex just-sb">
            <a-tag class="tag future font_14" v-if="item.finishTime">预计{{ item.finishTime }}完成</a-tag>
            <div class="time">{{ item.createTime }}</div>
          </div>
          <div class="depict white-space-pre-wrap color-65">
            {{ item.content }}
          </div>
          <div class="flex align-center">
            <div style="margin-right: 40px">
              <span class="name">{{ item.createName }}</span>
              <span class="sector">{{ item.orgPathName }}</span>
            </div>
          </div>
          <div class="file" @click="previewFile(item)">
            {{ item.fileName }}
          </div>
        </div>
      </div>
    </div>
    <div class="margin_t_20" v-if="dataDetail.hostReject == 1">
      <div>主办部门处理结果</div>
      <div class="flowPath margin_t_16">
        <div style="padding: 12px 16px">
          <div class="flex just-sb">
            <a-tag class="tag refuse font_14">已驳回</a-tag>
            <div class="time">
              {{ dataDetail.processList[dataDetail.processList.length - 1].createTime }}
            </div>
          </div>
          <div class="desc white-space-pre-wrap color-65">
            {{ dataDetail.processList[dataDetail.processList.length - 1].auditReason }}
          </div>
        </div>
      </div>
    </div>
    <div class="audit" v-if="showSubDepartmentSection">
      <div>协办部门反馈结果</div>
      <div class="flowPath" v-if="showSubDepartmentActions">
        <div style="padding: 12px 16px;gap: 16px;display:flex;">
          <a-button v-if="subUserList.find(item => item.userId == userInfo.id)?.appointed != true" @click="transferTask"
            type="primary" class="feedback-button">
            转派
          </a-button>
          <a-button @click="feedBack" type="primary" class="feedback-button">
            情况反馈
          </a-button>
        </div>
      </div>
    </div>
    <div class="margin_t_16" v-if="showSubFeedback && (isRefuse > 1 || dataDetail.hostReject == 1)">
      <div class="flowPath" v-for="(item, key) in getSubFeedbackList" :key="key">
        <a-button v-if="key == 0 && item.appointed && !hasUserSubmittedFeedback" @click="handleReview(item)"
          type="primary" class="feedback-button-margin">
          审核
        </a-button>
        <div style="padding: 12px 16px">
          <div class="flex just-sb">
            <a-tag class="tag future font_14" v-if="item.finishTime">预计{{ item.finishTime }}完成</a-tag>
            <div class="time">{{ item.createTime }}</div>
          </div>
          <div class="depict white-space-pre-wrap color-65">
            {{ item.content }}
          </div>
          <div class="flex align-center">
            <div style="margin-right: 40px">
              <span class="name">{{ item.createName }}</span>
              <span class="sector">{{ item.orgPathName }}</span>
            </div>
          </div>
          <div class="file" @click="previewFile(item)">{{ item.fileName }}</div>
        </div>
      </div>
    </div>
    <div class="submit flex align-center" v-if="userInfo.id == dataDetail.createBy && isRefuse == 3">
      <div>提交人评价意见</div>
      <a-button @click="unsolved" type="primary" class="unsolved-button">
        未解决
      </a-button>
      <a-button @click="resolved" type="primary" class="resolved-button">
        已解决
      </a-button>
    </div>
    <div class="submit" v-if="isRefuse == 4">
      <div>提交人评价意见</div>
      <div class="margin_t_16">
        <div class="flowPath">
          <div style="padding: 12px 16px">
            <div class="flex align-center">
              <div class="flowPath">
                <div>
                  <a-tag class="tag publish font_14 font-weight-400"
                    v-if="dataDetail.evaluation.auditResult == 1">已解决</a-tag>
                  <a-tag class="tag refuse font_14 font-weight-400" v-else>未解决</a-tag>
                </div>
              </div>
            </div>
            <div class="depict white-space-pre-wrap color-65 font-weight-400">
              {{ dataDetail.evaluation.content }}
            </div>
            <div class="file" @click="previewFile(dataDetail.evaluation)">
              {{ dataDetail.evaluation.fileName }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { defineComponent, reactive, toRefs, computed } from "vue";
export default defineComponent({
  emits: ["allot-view", "feed-back", "unsolved", "preview-file", "transfer-task", "review"],
  props: {
    dataDetail: {
      type: Object,
      default: () => ({
        processList: [],
        feedbackList: [],
        dealUserList: [],
        evaluation: {},
      }),
    },
  },
  setup(props, { emit }) {
    const data = reactive({
      dataDetail: props.dataDetail,
      userInfo: JSON.parse(localStorage.getItem("userInfo")),
      mainUserList: props.dataDetail.dealUserList.filter((item) => item.type == 1).map((item) => { return { userId: item.userId, status: item.status, appointed: item.appointed, distributed: item.distributed } }), // 主办及被转派人
      subUserList: props.dataDetail.dealUserList.filter((item) => item.type === 2).map((item) => { return { userId: item.userId, status: item.status, appointed: item.appointed, distributed: item.distributed } }), // 协办及被转派人
      mainBack: props.dataDetail.feedbackList.filter((item) => item.type == 1), // 不显示被转派人反馈信息
      subBack: props.dataDetail.feedbackList.filter((item) => item.type == 2), // 不显示被转派人反馈信息
      text: props.dataDetail.dealUserList.find((item) => item.type == 1)?.orgPathName,
      dealTime: props.dataDetail.processList.find((item) => item.process == 2)?.createTime,
      isRefuse: props.dataDetail.status, //status帖子状态(-1:已驳回;1:已发布;2:已分配;3:已反馈4:已完成)
      auditReason: props.dataDetail.processList.find(
        (item) => item.auditResult == 0)?.auditReason, //process流程状态(1:已发布;2:已分配;3:已办理(主办);4:已办理(协办);5:已确认)
    });
    // 判断当前用户是否为主办转派人
    const isAssigned = computed(() =>
      data.mainUserList.filter(
        (item) =>
          !item.appointed && item.userId == JSON.parse(localStorage.getItem("userInfo")).id
      )
    );
    // 判断当前用户是否为协办转派人
    const isAssignedSub = computed(() =>
      data.subUserList.filter(
        (item) =>
          !item.appointed && item.userId == JSON.parse(localStorage.getItem("userInfo")).id
      )
    );
    // 判断当前用户是否为主办被转派人
    const isAppointed = computed(() =>
      data.mainUserList.filter(
        (item) =>
          item.appointed && item.userId === JSON.parse(localStorage.getItem("userInfo")).id
      )
    );
    // 判断当前用户是否为协办被转派人
    const isAppointedSub = computed(() =>
      data.subUserList.filter(
        (item) =>
          item.appointed && item.userId === JSON.parse(localStorage.getItem("userInfo")).id
      )
    );
    // 获取当前用户作为被转派人提交的反馈信息
    const dealUserFeedbackList = computed(() =>
      props.dataDetail.feedbackList.filter(
        (item) =>
          (item.type === 1 || item.type === 2) &&
          item.appointed === true &&
          item.createBy === data.userInfo.id
      )
    )

    // 判断当前用户是否已经提交了反馈（作为转派人审核被转派人的反馈）
    const hasUserSubmittedFeedback = computed(() => {
      // 检查当前用户是否已经提交了反馈
      return props.dataDetail.feedbackList.some(item =>
        item.createBy === data.userInfo.id && !item.appointed
      );
    })
    // 判断是否显示协办反馈内容
    const showSubFeedback = computed(() => {
      // 发起人（创建者）只能看到非被转派人的反馈结果
      if (data.userInfo.id == data.dataDetail.createBy) {
        const nonAppointedSubFeedback = data.subBack.filter(item => !item.appointed);
        return nonAppointedSubFeedback.length > 0 && (data.isRefuse >= 2 || data.isRefuse == -1);
      }
      // 审核管理员只能看到非被转派人的反馈
      if (data.userInfo.roleKeyList?.includes('auditManager')) {
        const nonAppointedSubFeedback = data.subBack.filter(item => !item.appointed);
        return nonAppointedSubFeedback.length > 0 && (data.isRefuse >= 2 || data.isRefuse == -1);
      }
      // 协办相关人员只能看到自己及其被转派人的反馈
      const isSubUser = data.subUserList.some(item => item.userId === data.userInfo.id);
      if (isSubUser) {
        return getSubFeedbackList.value.length > 0 && (data.isRefuse >= 2 || data.isRefuse == -1);
      }
      // 主办相关人员可以看到协办的非被转派人反馈（协办人自己提交的信息）
      const isMainUser = data.mainUserList.some(item => item.userId === data.userInfo.id);
      if (isMainUser) {
        const nonAppointedSubFeedback = data.subBack.filter(item => !item.appointed);
        return nonAppointedSubFeedback.length > 0 && (data.isRefuse >= 2 || data.isRefuse == -1);
      }
      return false;
    })
    // 判断是否显示主办部门反馈结果区域（标题和操作按钮）
    const showMainDepartmentSection = computed(() => {
      // 发起人只能看到非被转派人的主办部门反馈结果
      if (data.userInfo.id == data.dataDetail.createBy) {
        const nonAppointedMainFeedback = data.mainBack.filter(item => !item.appointed);
        return nonAppointedMainFeedback.length > 0;
      }
      // 审核管理员只能看到非被转派人的主办部门反馈结果
      if (data.userInfo.roleKeyList?.includes('auditManager')) {
        const nonAppointedMainFeedback = data.mainBack.filter(item => !item.appointed);
        return nonAppointedMainFeedback.length > 0;
      }
      // 主办部门相关人员可以看到
      const isMainUser = data.mainUserList.some(item => item.userId == data.userInfo.id);
      if (isMainUser) {
        const currentUser = data.mainUserList.find(item => item.userId == data.userInfo.id);
        // 主办用户在分配状态时显示（无论是否有反馈），在其他状态时只有有自己的反馈才显示
        if (data.isRefuse == 2 && currentUser?.status == 0) {
          return true; // 分配状态时显示操作按钮
        }
        // 其他状态时检查是否有自己相关的反馈
        const hasOwnFeedback = getMainFeedbackList.value.length > 0;
        return (data.isRefuse == 3 && currentUser?.status == 0) ||
          data.isRefuse == 4 ||
          hasOwnFeedback;
      }
      // 其他情况：不显示（避免协办用户看到主办部门反馈结果标题）
      return false;
    })
    // 判断是否显示主办部门操作按钮（转派、情况反馈）
    const showMainDepartmentActions = computed(() => {
      // 只有主办部门相关人员且状态为已分配时才显示操作按钮
      if (data.isRefuse == 2) {
        const isMainUser = data.mainUserList.some(item => item.userId == data.userInfo.id);
        if (isMainUser) {
          const currentUser = data.mainUserList.find(item => item.userId == data.userInfo.id);
          // 主办用户且未被分配（distributed）时显示操作按钮
          return !currentUser?.distributed;
        }
      }
      // 主办用户在反馈状态也应该能操作，除非已转派
      if (data.isRefuse >= 2) {
        const isMainUser = data.mainUserList.some(item => item.userId == data.userInfo.id);
        if (isMainUser) {
          const currentUser = data.mainUserList.find(item => item.userId == data.userInfo.id);
          return !currentUser?.distributed;
        }
      }
      return false;
    })
    // 判断是否显示协办部门反馈结果区域（标题和操作按钮）
    const showSubDepartmentSection = computed(() => {
      // 发起人只能看到非被转派人的协办部门反馈结果
      if (data.userInfo.id == data.dataDetail.createBy) {
        const nonAppointedSubFeedback = data.subBack.filter(item => !item.appointed);
        return nonAppointedSubFeedback.length > 0;
      }
      // 审核管理员只能看到非被转派人的协办部门反馈结果
      if (data.userInfo.roleKeyList?.includes('auditManager')) {
        const nonAppointedSubFeedback = data.subBack.filter(item => !item.appointed);
        return nonAppointedSubFeedback.length > 0;
      }
      // 协办部门相关人员可以看到
      const isSubUser = data.subUserList.some(item => item.userId == data.userInfo.id);
      if (isSubUser) {
        const currentUser = data.subUserList.find(item => item.userId == data.userInfo.id);
        // 协办用户在分配状态时显示（无论是否有反馈），在其他状态时只有有自己的反馈才显示
        if (data.isRefuse == 2 && currentUser?.status == 0) {
          return true; // 分配状态时显示操作按钮
        }
        // 其他状态时检查是否有自己相关的反馈
        const hasOwnFeedback = getSubFeedbackList.value.length > 0;
        return (data.isRefuse > 2 || data.dataDetail.hostReject == 1) && hasOwnFeedback;
      }
      // 如果当前用户是主办，需要特殊处理
      if (isAssigned.value.length > 0) {
        // 主办用户只能看到协办的非被转派人反馈，如果没有非被转派人反馈则不显示标题
        const nonAppointedSubFeedback = data.subBack.filter(item => !item.appointed);
        return nonAppointedSubFeedback.length > 0 && (data.isRefuse > 2 || data.dataDetail.hostReject == 1);
      }
      // 其他情况：如果有协办反馈且状态合适，显示
      return data.subBack.length > 0 && (data.isRefuse > 2 || data.dataDetail.hostReject == 1);
    })
    // 判断是否显示协办部门操作按钮（转派、情况反馈）
    const showSubDepartmentActions = computed(() => {
      // 只有协办部门相关人员且状态为已分配时才显示操作按钮
      if (data.isRefuse == 2) {
        const isSubUser = data.subUserList.some(item => item.userId == data.userInfo.id);
        if (isSubUser) {
          const currentUser = data.subUserList.find(item => item.userId == data.userInfo.id);
          // 协办用户且未被分配（distributed）时显示操作按钮
          return !currentUser?.distributed;
        }
      }
      // 添加额外条件：如果用户是协办人员且状态为已分配（2）或已反馈（3）并且用户有权限，则显示操作按钮
      if (data.isRefuse >= 2) {
        const isSubUser = data.subUserList.some(item => item.userId == data.userInfo.id);
        if (isSubUser) {
          const currentUser = data.subUserList.find(item => item.userId == data.userInfo.id);
          // 即使在反馈状态，协办用户也应能进行反馈操作，但已转派的用户不能操作
          return !currentUser?.distributed;
        }
      }
      return false;
    })
    // 判断是否显示主办反馈内容
    const showMainFeedback = computed(() => {
      // 发起人（创建者）只能看到非被转派人的反馈结果
      if (data.userInfo.id == data.dataDetail.createBy) {
        const nonAppointedMainFeedback = data.mainBack.filter(item => !item.appointed);
        return nonAppointedMainFeedback.length > 0 && (data.isRefuse >= 2 || data.isRefuse == -1);
      }
      // 审核管理员只能看到非被转派人的反馈
      if (data.userInfo.roleKeyList?.includes('auditManager')) {
        const nonAppointedMainFeedback = data.mainBack.filter(item => !item.appointed);
        return nonAppointedMainFeedback.length > 0 && (data.isRefuse >= 2 || data.isRefuse == -1);
      }

      if (data.mainBack.length === 0) return false;

      const currentUser = data.mainUserList.find(item => item.userId == data.userInfo.id);
      const isMainUser = currentUser !== undefined;

      // 主办相关人员只能看到自己的反馈
      if (isMainUser) {
        return getMainFeedbackList.value.length > 0 && (data.isRefuse >= 2 || data.isRefuse == -1);
      }

      return false;
    })
    // 获取协办反馈列表
    const getSubFeedbackList = computed(() => {
      // 如果当前用户是协办转派人，显示自己的反馈和由自己转派的被转派人反馈
      if (isAssignedSub.value.length > 0) {
        // 协办转派人能看到：1. 自己创建的反馈 2. 由自己转派的被转派人反馈
        return data.subBack.filter(item =>
          (item.createBy === data.userInfo.id) || // 自己创建的反馈
          (item.appointBy === data.userInfo.id)   // 由自己转派的被转派人反馈
        );
      }
      // 如果当前用户是协办被转派人，只显示自己的反馈
      if (isAppointedSub.value.length > 0) {
        // 协办被转派人只能看到自己创建的反馈
        return data.subBack.filter(item => item.appointed && item.createBy === data.userInfo.id);
      }
      // 如果当前用户是协办处理人（非转派），只显示自己的反馈
      if (data.subUserList.filter(item => !item.appointed && item.userId === data.userInfo.id).length > 0) {
        // 协办处理人只能看到自己创建的反馈
        return data.subBack.filter(item => !item.appointed && item.createBy === data.userInfo.id);
      }
      // 如果当前用户是主办相关人员，只显示协办的非被转派人反馈（协办人自己提交的信息）
      if (isAssigned.value.length > 0) {
        return data.subBack.filter(item => !item.appointed);
      }
      // 其他情况（如发起人、审核管理员等）只显示非被转派人的协办反馈
      return data.subBack.filter(item => !item.appointed);
    })
    // 获取主办反馈列表
    const getMainFeedbackList = computed(() => {
      // 如果当前用户是主办转派人，显示自己的反馈和由自己转派的被转派人反馈
      if (isAssigned.value.length > 0) {
        // 主办转派人能看到：1. 自己创建的反馈 2. 由自己转派的被转派人反馈
        return data.mainBack.filter(item =>
          (item.createBy === data.userInfo.id) || // 自己创建的反馈
          (item.appointBy === data.userInfo.id)   // 由自己转派的被转派人反馈
        );
      }
      // 如果当前用户是主办被转派人，只显示自己的反馈
      if (isAppointed.value.length > 0) {
        // 主办被转派人只能看到自己创建的反馈
        return data.mainBack.filter(item => item.appointed && item.createBy === data.userInfo.id);
      }
      // 如果当前用户是主办处理人（非转派），只显示自己的反馈
      if (data.mainUserList.filter(item => !item.appointed && item.userId === data.userInfo.id).length > 0) {
        // 主办处理人只能看到自己创建的反馈
        return data.mainBack.filter(item => !item.appointed && item.createBy === data.userInfo.id);
      }
      // 其他情况（如发起人、审核管理员等）只显示非被转派人的主办反馈
      return data.mainBack.filter(item => !item.appointed);
    })
    // 分配
    const allotView = () => {
      emit("allot-view");
    };
    // 情况反馈
    const feedBack = () => {
      let noRefuse = false;
      if (data.subUserList.some(item => item.userId == data.userInfo.id) || data.mainUserList.find(item => item.userId == data.userInfo.id)?.appointed == true) {
        // 是协办或主办里的被转派人，都不显示拒绝按钮
        noRefuse = true;
      }
      let params = {
        where: "feedBack",
        type: 1,
        noRefuse: noRefuse,
      };
      emit("feed-back", params);
    };
    // 转派
    const transferTask = () => {
      emit("transfer-task");
    };
    // 处理审核
    const handleReview = (item) => {
      let params = {
        type: 1,
        item
      };
      emit("review", params);
    };
    //未解决反馈
    const unsolved = () => {
      let params = {
        type: 2,
        where: "idea",
      };
      emit("unsolved", params);
    };
    // 已解决反馈
    const resolved = () => {
      let params = {
        type: 1,
        where: "idea",
      };
      emit("unsolved", params);
    };
    // 预览文件
    const previewFile = async (e) => {
      try {
        const { fileUrl: href, fileName: downName } = e;
        const token = localStorage.getItem("token") || "";
        const fileExtension = downName.split(".").pop().toLowerCase();
        const isImage = ["jpg", "jpeg", "png"].includes(fileExtension);
        const isVideo = ["mp4", "mov", "avi", "wmv", "mkv", "flv"].includes(
          fileExtension
        );
        if (isVideo) {
          let type = 1;
          emit("preview-file", e, type);
        } else if (isImage) {
          let type = 2;
          emit("preview-file", e, type);
        } else {
          let downloadUrl = new URL(href, window.location.origin).href;
          if (href.includes(window.location.origin)) {
            downloadUrl = href.replace(window.location.origin, "/portal");
          }
          const urlObj = new URL(downloadUrl);
          urlObj.searchParams.set("token", token);
          downloadUrl = urlObj.toString();
          window.open(downloadUrl, "_blank");
        }
      } catch (error) {
        console.error("文件处理失败:", error);
      }
      return false;
    };
    return {
      ...toRefs(data),
      allotView,
      feedBack,
      previewFile,
      resolved,
      unsolved,
      transferTask,
      handleReview,
      dealUserFeedbackList,
      hasUserSubmittedFeedback,
      showSubFeedback,
      showMainFeedback,
      getSubFeedbackList,
      getMainFeedbackList,
      isAssigned,
      isAssignedSub,
      isAppointed,
      isAppointedSub,
      showMainDepartmentSection,
      showSubDepartmentSection,
      showMainDepartmentActions,
      showSubDepartmentActions,
    };
  },
});
</script>
<style lang="scss" scoped>
.audit {
  margin-top: 24px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.submit {
  margin-top: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.flowPath {
  background: #f5f7fc;
  border-radius: 4px;

  .publish {
    background: rgba(12, 112, 235, 0.1);
    color: #0c70eb;
    border-radius: 4px;
    border: none;
  }

  .refuse {
    background: rgba(245, 29, 15, 0.1);
    color: #f51d0f;
    border-radius: 4px;
    border: none;
  }

  .future {
    background: rgba(246, 118, 0, 0.1);
    color: #f67600;
    border-radius: 4px;
    border: none;
  }

  .desc {
    color: rgba(0, 0, 0, 0.65);
    margin-top: 9px;
  }

  .depict {
    color: rgba(0, 0, 0, 0.85);
    margin-top: 9px;
    width: 100%;
    word-wrap: break-word;
  }

  .time {
    color: rgba(0, 0, 0, 0.25);
  }

  .name {
    color: rgba(0, 0, 0, 0.45);
    margin-right: 12px;
  }

  .sector {
    color: rgba(0, 0, 0, 0.45);
  }
}

.file {
  font-weight: 400;
  font-size: 14px;
  color: #0c70eb;
  cursor: pointer;
}

.audit-button {
  background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
  border-radius: 4px;
  font-weight: 500;
  border: none;
}

.audit-button-margin-left {
  background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
  border-radius: 4px;
  font-weight: 500;
  border: none;
  margin-left: 16px;
}

.feedback-button {
  background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
  border-radius: 4px;
  font-weight: 500;
  border: none;
}

.feedback-button-margin {
  background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
  border-radius: 4px;
  font-weight: 500;
  border: none;
  margin: 12px 0 0 16px;
}

.unsolved-button {
  background: rgba(1, 61, 253, 0.1);
  border-radius: 4px;
  font-weight: 500;
  border: none;
  margin-left: 16px;
  color: #0c70eb;
}

.resolved-button {
  background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
  border-radius: 4px;
  font-weight: 500;
  border: none;
  margin-left: 16px;
}

.white-space-pre-wrap {
  white-space: pre-wrap;
}

.color-65 {
  color: rgba(0, 0, 0, 0.65);
}

.color-85 {
  color: rgba(0, 0, 0, 0.85);
}

.font-weight-400 {
  font-weight: 400;
}
</style>