.contentCommunity {
  background: #F5F7FC;
  width: 100%;
  min-height: calc(100vh - 60px);
  background-image: url('../../assets/images/commiunty/banner.png');
  background-size: contain;
  background-repeat: no-repeat;
  // padding-bottom: 32px;
  padding: 73px calc((100vw - 1200px)/2);
  display: flex;
  flex-direction: column;

  .top {
    font-weight: 400;
    font-size: 16px;
    color: #2B3F66;
    line-height: 32px;
    text-align: left;

    .top_title {
      font-weight: bold;
      font-size: 42px;
      color: #24456A;
    }
  }

  .searchBar {
    display: flex;
    justify-content: space-between;

    .spanBox {
      background: #F5F7FC;
      border-radius: 4px 4px 4px 4px;
      width: 100px;
      line-height: 46px;
      height: 48px;
      font-weight: 500;
      font-size: 16px;
      color: #0C70EB;
      text-align: center;
      cursor: pointer;
      margin-right: 8px;
    }

    .activeBtn {
      background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
      font-weight: bold;
      color: #FFFFFF;
    }

    .seekInput {
      display: flex;

      .seekInfo {
        font-weight: 500;
        font-size: 16px;
        color: #FFFFFF;
        line-height: 16px;
        display: flex;
        align-items: center;
        width: 83px;
        height: 48px;
        background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
        cursor: pointer;
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;

        img {
          width: 16px;
          height: 16px;
          margin-left: 16px;
          margin-right: 4px;
        }
      }

      .inputClass {
        border: none;
        width: 340px;
        height: 48px;
        box-shadow: none !important;
        border-radius: 6px 0px 0px 6px;
      }
    }
  }

  .left {
    flex-shrink: 0;
    margin-top: 32px;
    width: 858px;
    background-color: #FFFFFF;
    padding: 0px 0px;

    .card_content {
      border-bottom: 1px solid #DAE2F5;

      .card_text {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;

        .title {
          font-weight: bold;
          font-size: 18px;
          color: rgba(0, 0, 0, 0.85);
        }
        .timeout-status {
          margin-left: 10px;
          display: flex;
          align-items: center;
          font-weight: 400;
          font-size: 12px;
          text-align: left;
          img {
            width: 20px;
            height: 20px;
          }
        }

        .overtime-tag {
          padding: 2px 2px;
          display: flex;
          align-items: center;
          color: #F51D0F;
          gap: 4px;
          width: 65px;
        }
        

        .countdown-tag {
          padding: 2px 2px;
          display: flex;
          color: #F67600;
          align-items: center;
          gap: 4px;
          width: 78px;
        }

        .card_tag {
          margin-right: 8px;
          font-weight: 400;
          font-size: 12px;
          color: rgba(0, 0, 0, 0.65);
          border-radius: 2px 2px 2px 2px;
          padding: 3px 8px;
          line-height: 22px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .tag-suggestion {
          background: rgba(0,189,98,0.1);
          color: #00BD62;
        }

        .tag-announcement {
          background: rgba(12,112,235,0.1);
          color: #0C70EB;
        }

        .tag-bug {
          background: rgba(246,118,0,0.1);
          color: #F67600;
        }

        span {
          width: 52px;
          height: 20px;
          border-radius: 4px 4px 4px 4px;
          font-weight: 400;
          font-size: 12px;
          text-align: center;
        }

        .toTop {
          display: inline-block;
          width: 40px;
          height: 20px;
          background: linear-gradient(90deg, #FF3730 0%, #FF845F 100%);
          border-radius: 2px 2px 2px 2px;
          font-weight: 500;
          font-size: 12px;
          color: #FFFFFF;
          line-height: 18px;
          margin-left: 16px;
        }

        .Rejected {
          background: rgba(245, 29, 15, 0.1);
          color: #F51D0F;
        }

        .Published {
          background: rgba(12, 34, 235, 0.1);
          color: #0C22EB;
        }

        .Assigned {
          background: rgba(12, 112, 235, 0.1);
          color: #0C70EB;
        }

        .processed {
          background: rgba(246, 118, 0, 0.1);
          color: #F67600;
        }

        .Completed {
          background: rgba(0, 189, 98, 0.1);
          color: #00BD62;
        }
      }

      .card_desc {
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 95%;
        margin-bottom: 16px;
        word-break: break-all;
      }

    }

    .layPage {
      width: 100%;
      text-align: right;
      margin: 15px 0;
      padding-right: 34px;
    }
  }

  .right {
    margin-top: 32px;
    flex-shrink: 0;

    .action {
      padding: 16px;
      background-color: #FFFFFF;
      border-radius: 8px 8px 8px 8px;
      margin-bottom: 24px;

      .btn {
        background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
        border-radius: 6px 6px 6px 6px;
        font-weight: 500;
        font-size: 16px;
        color: #FFFFFF;
        padding: 15px 68px;
        align-items: center;
        cursor: pointer;
      }
    }

    .right_top {
      background: #FFFFFF;
      box-shadow: 0px 4px 4px 0px rgba(176, 204, 230, 0.15);
      border-radius: 8px 8px 8px 8px;
      padding-bottom: 1px;
      margin-bottom: 16px;

      .top {
        background: rgba(182, 215, 255, 0.1);
        padding: 16px;
        font-weight: bold;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        align-items: center;

        .line {
          width: 4px;
          height: 12px;
          background: linear-gradient(360deg, #0142FD 0%, #2475F9 100%);
          margin-right: 12px;
        }
      }

      .li_box {
        margin: 32px;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        width: calc(100% - 64px);

        .li_con {
          justify-content: space-between;
          margin-bottom: 24px;
        }

        .num {
          font-weight: bold;
          font-size: 18px;
          color: #0C70EB;
        }
      }
    }
  }
}

.emptyPhotos {
  // margin: auto;
  background-color: #FFFFFF;
  width: 858px;
  margin-top: 32px;
  display: flex;
  align-items: center;
  justify-content: center;

  .imgs {
    width: 240px;
    height: 248px;
  }
}

.selected {
  font-weight: bold;
}

.form-row {
  display: flex;
  min-height: 48px;
  // border-bottom: 1px solid #e8e8e8;
  align-items: stretch;
  /* 关键属性：让子元素撑满高度 */
}

.form-row:last-child {
  border-bottom: none;
}

.label-col {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 16px;
  /* 上下 padding 改为 0 */
  margin: 0;
  /* 确保没有 margin */
  height: auto;
  /* 确保高度自适应 */
}

.input-col {
  padding: 8px 16px;
  display: flex;
  /* 新增 */
  align-items: center;
  /* 确保内容垂直居中 */
}

.label-text {
  font-weight: 400;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  padding-right: 16px;
}

.form-row,
.form-row * {
  box-sizing: border-box;
}

.ant-col {
  margin: 0;
  padding: 0;
}

.custom-upload-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  background: #fafafa;
  width: 100%;
}

.poiner {
  cursor: pointer;
}

.custom-upload-item .anticon {
  margin-right: 8px;
  font-size: 16px;
  color: #1890ff;
}

.file-name {
  flex: 1;
  padding-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
}

.delete-btn {
  flex-shrink: 0;
  color: #ff4d4f;
}

.delete-btn:hover {
  background: rgba(255, 77, 79, 0.1);
}

.mypage {
  .ant-pagination {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-select-selector {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-pagination-item-active {
    background: #0C70EB;
    border: none;
  }

  .ant-pagination-item-active a {
    color: #ffffff;
  }

  .ant-pagination-item-active:focus a,
  .ant-pagination-item-active:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-active a:focus,
  .ant-pagination-item-active a:hover {
    color: #ffffff;
  }

  .ant-pagination-item:focus,
  .ant-pagination-item:hover {
    background: #007eff;
  }

  .ant-pagination-item:focus a,
  .ant-pagination-item:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-disabled:hover,
  .ant-pagination-item-disabled:focus {
    background-color: #ffffff;
    border-color: #d9d9d9;

    a {
      color: rgba(0, 0, 0, 0.25);
    }
  }

  .ant-pagination-options-size-changer.ant-select {
    margin-right: 0;
  }

  .ant-pagination-options-quick-jumper {
    margin-left: 8px;
  }
}