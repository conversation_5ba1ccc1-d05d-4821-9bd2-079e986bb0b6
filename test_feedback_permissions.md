# 协办反馈权限控制修改说明

## 问题描述
每个协办只能看到自己协办反馈的内容，其他协办反馈和主办都不可见，feedbacklist中appointBy代表转派人的id。

## 修改内容

### 1. 协办反馈列表权限控制 (getSubFeedbackList)
- **协办转派人**: 只能看到由自己转派的反馈 (appointBy === 当前用户ID)
- **协办被转派人**: 只能看到自己创建的反馈 (appointed && createBy === 当前用户ID)
- **协办处理人**: 只能看到自己创建的反馈 (!appointed && createBy === 当前用户ID)
- **主办相关人员**: 可以看到协办的非被转派人反馈
- **发起人/审核管理员**: 可以看到所有协办反馈

### 2. 主办反馈列表权限控制 (getMainFeedbackList)
- **主办转派人**: 只能看到由自己转派的反馈 (appointBy === 当前用户ID)
- **主办被转派人**: 只能看到自己创建的反馈 (appointed && createBy === 当前用户ID)
- **主办处理人**: 只能看到自己创建的反馈 (!appointed && createBy === 当前用户ID)
- **发起人/审核管理员**: 可以看到所有主办反馈

### 3. 反馈内容显示权限 (showSubFeedback/showMainFeedback)
- **协办/主办相关人员**: 只能看到自己相关的反馈内容
- **发起人**: 可以看到所有反馈
- **审核管理员**: 可以看到所有反馈

### 4. 反馈区域显示权限 (showSubDepartmentSection/showMainDepartmentSection)
- **协办/主办用户**: 在分配状态时显示操作按钮，在其他状态时只有有自己的反馈才显示
- **发起人/审核管理员**: 可以看到所有反馈区域

## 核心逻辑
1. 每个协办用户只能看到自己创建的反馈或由自己转派的反馈
2. appointBy字段用于标识转派人ID，转派人可以看到被转派人的反馈
3. createBy字段用于标识反馈创建者，用户只能看到自己创建的反馈
4. 发起人和审核管理员保持原有权限，可以看到所有反馈

## 测试场景
1. 协办A转派给协办B，协办A应该能看到协办B的反馈
2. 协办A不应该看到协办C的反馈（除非是自己转派的）
3. 主办用户不应该看到协办的被转派人反馈
4. 发起人和审核管理员应该能看到所有反馈
