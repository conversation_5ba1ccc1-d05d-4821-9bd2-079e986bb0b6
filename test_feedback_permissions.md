# 协办反馈权限控制修改说明

## 问题描述
转派逻辑：主/协办转派给被转派人，如果被转派人反馈了，主/协办要能看到，主/协办可以审核被转派人提交的信息，提交后会feedbacklist会新增一条主/协办提交的信息，只要主/协办提交了反馈，审核按钮就不要显示了，且主办要显示自己及自己制定的转派人提交的信息和协办人自己提交的信息，协办只显示自己及其被转派人提交的信息，不显示主办和其他协办的信息。

## 修改内容

### 1. 协办反馈列表权限控制 (getSubFeedbackList)
- **协办转派人**: 显示自己的反馈和由自己转派的被转派人反馈 (createBy === 当前用户ID || appointBy === 当前用户ID)
- **协办被转派人**: 只能看到自己创建的反馈 (appointed && createBy === 当前用户ID)
- **协办处理人**: 只能看到自己创建的反馈 (!appointed && createBy === 当前用户ID)
- **主办相关人员**: 可以看到协办的非被转派人反馈（协办人自己提交的信息）
- **发起人/审核管理员**: 只能看到非被转派人的协办反馈 (!appointed)

### 2. 主办反馈列表权限控制 (getMainFeedbackList)
- **主办转派人**: 显示自己的反馈和由自己转派的被转派人反馈 (createBy === 当前用户ID || appointBy === 当前用户ID)
- **主办被转派人**: 只能看到自己创建的反馈 (appointed && createBy === 当前用户ID)
- **主办处理人**: 只能看到自己创建的反馈 (!appointed && createBy === 当前用户ID)
- **发起人/审核管理员**: 只能看到非被转派人的主办反馈 (!appointed)

### 3. 审核按钮显示控制 (hasUserSubmittedFeedback)
- **新增逻辑**: 只要主/协办提交了反馈，审核按钮就不显示
- **判断条件**: 检查当前用户是否已经提交了非被转派人的反馈 (createBy === 当前用户ID && !appointed)

### 4. 反馈内容显示权限 (showSubFeedback/showMainFeedback)
- **协办相关人员**: 只能看到自己及其被转派人的反馈
- **主办相关人员**: 可以看到自己的反馈、自己转派的被转派人反馈、协办人自己提交的反馈
- **发起人**: 只能看到非被转派人的反馈
- **审核管理员**: 只能看到非被转派人的反馈

## 核心逻辑
1. **转派人权限**: 主/协办转派给被转派人后，转派人能看到被转派人的反馈
2. **审核机制**: 主/协办可以审核被转派人的信息，审核后会新增一条主/协办提交的反馈
3. **审核按钮控制**: 只要主/协办提交了反馈，审核按钮就不再显示
4. **主办显示范围**: 自己的反馈 + 自己转派的被转派人反馈 + 协办人自己提交的反馈
5. **协办显示范围**: 自己的反馈 + 自己转派的被转派人反馈
6. **隔离原则**: 协办不显示主办和其他协办的信息

## 测试场景
1. 主办A转派给主办B，主办A应该能看到主办B的反馈
2. 协办C转派给协办D，协办C应该能看到协办D的反馈
3. 协办C不应该看到主办A、主办B的反馈
4. 协办C不应该看到其他协办E的反馈（除非是自己转派的）
5. 主办A可以看到协办人自己提交的反馈，但不能看到协办的被转派人反馈
6. 主/协办提交反馈后，审核按钮不再显示
7. 发起人和审核管理员只能看到非被转派人的反馈
